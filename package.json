{"name": "JAIBET", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @JAIBET/backend dev", "dev:setup": "turbo -F @JAIBET/backend dev:setup"}, "dependencies": {"@privy-io/react-auth": "^3.0.1", "@solana-program/system": "^0.8.0", "@solana-program/token": "^0.6.0", "@solana/spl-token": "^0.4.14"}, "devDependencies": {"@biomejs/biome": "2.2.4", "shadcn": "^3.3.1", "turbo": "^2.5.4", "ultracite": "5.4.4"}, "packageManager": "bun@1.2.19"}