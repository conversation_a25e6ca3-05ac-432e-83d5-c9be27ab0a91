"use client";

import { useMemo, useState } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { Wallet, ShieldCheck } from "lucide-react";
import LoginModal from "@/components/login-modal";

const bulletPoints = [
	"One-tap entry with social login, embedded wallet, or Phantom when you prefer custody.",
	"A curated Solana market feed that surfaces the most disputed headlines and on-chain price signals.",
	"Fast fiat funding via Stripe Stablecoin payments and an instant Transak hand-off when needed.",
];

export default function Home() {
	const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
	const { ready, authenticated, user } = usePrivy();

	const statusLabel = useMemo(() => {
		if (!ready) {
			return "Preparing secure session...";
		}
		if (authenticated) {
			if (user?.google?.email) {
				return `Connected via Google: ${user.google.email}`;
			}
			if (user?.twitter?.username) {
				return `Connected via X: @${user.twitter.username}`;
			}
			if (user?.email?.address) {
				return `Connected via Email: ${user.email.address}`;
			}
			// Check for linked wallets
			const walletAccount = user?.linkedAccounts?.find(account => account.type === 'wallet');
			if (walletAccount?.address) {
				return `Solana Wallet Connected: ${walletAccount.address.slice(0, 4)}...${walletAccount.address.slice(-4)}`;
			}
			return "Successfully connected";
		}
		return "Sign in to start predicting";
	}, [ready, authenticated, user]);

	const handleLogin = () => {
		if (!ready || authenticated) {
			return;
		}
		setIsLoginModalOpen(true);
	};

	return (
		<main className="flex flex-1 flex-col justify-center px-4 py-10">
			<section className="mx-auto flex w-full max-w-3xl flex-col gap-8 rounded-2xl border border-border/60 bg-background/60 p-8 shadow-sm backdrop-blur-sm">
				<header className="flex flex-col gap-4">
					<p className="text-xs uppercase tracking-[0.3em] text-muted-foreground">
						Solana Prediction Markets · Powered by JAIBET
					</p>
					<h1 className="text-balance text-4xl font-semibold leading-tight sm:text-5xl">
						Bet on the headlines, settle in seconds.
					</h1>
					<p className="text-lg text-muted-foreground">
						JAIBET brings Polymarket-grade markets to Solana with optimistic news resolution, oracle-backed price markets, and a mobile-first wallet experience that clears friction from login to first trade.
					</p>
				</header>
				<section className="grid gap-3" aria-label="Product pillars">
					{bulletPoints.map((point) => (
						<div key={point} className="flex items-start gap-3 rounded-xl border border-border/80 bg-muted/20 p-4">
							<div className="mt-1 h-2.5 w-2.5 shrink-0 rounded-full bg-primary" aria-hidden="true" />
							<p className="text-sm leading-relaxed text-muted-foreground">{point}</p>
						</div>
					))}
				</section>
				<section className="flex flex-col items-start gap-4" aria-live="polite">
					<p className="text-sm font-medium text-muted-foreground">{statusLabel}</p>

					{!authenticated && (
						<>
							<button
								type="button"
								onClick={handleLogin}
								disabled={!ready}
								className="inline-flex items-center justify-center gap-2 rounded-full bg-primary px-6 py-3 text-sm font-semibold text-primary-foreground transition hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary disabled:cursor-not-allowed disabled:opacity-70"
							>
								<ShieldCheck className="h-4 w-4" />
								Sign In to Get Started
							</button>

							<div className="grid gap-3 sm:grid-cols-3">
								<div className="flex items-center gap-2 text-xs text-muted-foreground">
									<Wallet className="h-4 w-4 text-purple-500" />
									<span>Solana Wallets</span>
								</div>
								<div className="flex items-center gap-2 text-xs text-muted-foreground">
									<div className="h-4 w-4 rounded-full bg-blue-500" />
									<span>Google Sign-In</span>
								</div>
								<div className="flex items-center gap-2 text-xs text-muted-foreground">
									<div className="h-4 w-4 rounded-full bg-slate-500" />
									<span>X (Twitter)</span>
								</div>
							</div>

							<p className="text-xs text-muted-foreground">
								We use Privy to create embedded Solana wallets automatically and let you connect existing wallets like Phantom anytime.
							</p>
						</>
					)}

					{authenticated && (
						<div className="rounded-xl bg-emerald-50 border border-emerald-200 p-4">
							<div className="flex items-center gap-2 text-emerald-700">
								<ShieldCheck className="h-5 w-5" />
								<span className="font-medium">Ready to trade!</span>
							</div>
							<p className="text-sm text-emerald-600 mt-1">
								Your account is set up and ready. Start exploring prediction markets.
							</p>
						</div>
					)}
				</section>
			</section>
			<LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />
		</main>
	);
}
