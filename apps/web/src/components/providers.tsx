"use client";

import { PrivyProvider } from "@privy-io/react-auth";
import { toSolanaWalletConnectors } from "@privy-io/react-auth/solana";
import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ThemeProvider } from "./theme-provider";
import { Toaster } from "./ui/sonner";

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

const solanaConnectors = toSolanaWalletConnectors({
	shouldAutoConnect: true,
});

const privyConfig = {
	loginMethods: ["wallet", "google", "twitter", "email"] as const,
	appearance: {
		walletChainType: "solana-only" as const,
		showWalletLoginFirst: true,
		theme: "light" as const,
	},
	externalWallets: {
		solana: {
			connectors: solanaConnectors,
		},
	},
	embeddedWallets: {
		createOnLogin: "users-without-wallets" as const,
		requireUserPasswordOnCreate: false,
	},
} as const;

export default function Providers({ children }: { children: React.ReactNode }) {
	const privyAppId = process.env.NEXT_PUBLIC_PRIVY_APP_ID;
	if (!privyAppId) {
		throw new Error("NEXT_PUBLIC_PRIVY_APP_ID is required for PrivyProvider");
	}

	return (
		<PrivyProvider appId={privyAppId} config={privyConfig}>
			<ThemeProvider
				attribute="class"
				defaultTheme="system"
				enableSystem
				disableTransitionOnChange
			>
				<ConvexProvider client={convex}>{children}</ConvexProvider>
				<Toaster richColors />
			</ThemeProvider>
		</PrivyProvider>
	);
}
