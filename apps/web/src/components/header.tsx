"use client";

import { useState } from "react";
import Link from "next/link";
import { usePrivy } from "@privy-io/react-auth";
import { LogOut, User, Wallet, ChevronDown } from "lucide-react";
import { ModeToggle } from "./mode-toggle";
import LoginModal from "./login-modal";

const formatAddress = (address: string) => {
	if (address.length <= 8) return address;
	return `${address.slice(0, 4)}...${address.slice(-4)}`;
};

export default function Header() {
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
	const { ready, authenticated, user, logout } = usePrivy();

	const getUserDisplay = () => {
		if (user?.google?.email) {
			return user.google.email;
		}
		if (user?.twitter?.username) {
			return `@${user.twitter.username}`;
		}
		if (user?.email?.address) {
			return user.email.address;
		}
		// Check for linked wallets
		const walletAccount = user?.linkedAccounts?.find(account => account.type === 'wallet');
		if (walletAccount?.address) {
			return formatAddress(walletAccount.address);
		}
		return "Connected";
	};

	const handleLogout = async () => {
		await logout();
		setIsDropdownOpen(false);
	};

	return (
		<>
			<header className="w-full border-b border-border/60 bg-background/70 backdrop-blur">
				<div className="mx-auto flex w-full max-w-5xl items-center justify-between px-4 py-3">
					<Link href="/" className="text-sm font-semibold tracking-[0.2em] uppercase">
						JAIBET
					</Link>
					<nav aria-label="Site">
						<ul className="flex flex-wrap items-center gap-4 text-xs font-medium uppercase tracking-[0.25em] text-muted-foreground sm:gap-6">
							<li>
								<span>Solana Prediction Markets</span>
							</li>
							<li>
								<span>Optimistic + Oracle Resolution</span>
							</li>
						</ul>
					</nav>
					<div className="flex items-center gap-2">
						<ModeToggle />

						{!ready ? (
							<div className="h-9 w-24 animate-pulse rounded-full bg-muted" />
						) : !authenticated ? (
							<button
								type="button"
								onClick={() => setIsLoginModalOpen(true)}
								className="inline-flex h-9 items-center justify-center rounded-full border border-primary px-4 text-sm font-semibold text-primary transition hover:bg-primary hover:text-primary-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
							>
								Sign In
							</button>
						) : (
							<div className="relative">
								<button
									type="button"
									onClick={() => setIsDropdownOpen(!isDropdownOpen)}
									className="inline-flex h-9 items-center gap-2 rounded-full bg-emerald-500/15 px-4 py-1.5 text-sm font-medium text-emerald-700 transition hover:bg-emerald-500/20"
								>
									<span className="grid h-6 w-6 place-items-center rounded-full bg-emerald-500/20 ring-1 ring-emerald-500/40">
										{user?.google?.email || user?.twitter?.username ? (
											<User className="h-3 w-3" />
										) : (
											<Wallet className="h-3 w-3" />
										)}
									</span>
									<span className="max-w-32 truncate">{getUserDisplay()}</span>
									<ChevronDown className="h-3 w-3" />
								</button>

								{isDropdownOpen && (
									<div className="absolute right-0 top-full mt-1 w-48 rounded-lg border bg-background/95 p-2 shadow-lg backdrop-blur-sm">
										<div className="mb-2 border-b pb-2">
											<p className="text-xs text-muted-foreground">Connected as</p>
											<p className="truncate text-sm font-medium">{getUserDisplay()}</p>
										</div>
										<button
											type="button"
											onClick={handleLogout}
											className="flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm text-red-600 transition hover:bg-red-50"
										>
											<LogOut className="h-4 w-4" />
											Sign Out
										</button>
									</div>
								)}
							</div>
						)}
					</div>
				</div>
			</header>
			<LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />
		</>
	);
}
