"use client";

import { useState } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { useSolanaWallets } from "@privy-io/react-auth/solana";
import { X, Wallet, Mail, LogIn } from "lucide-react";

interface AuthModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export default function AuthModal({ isOpen, onClose }: AuthModalProps) {
	const { login, ready, authenticated } = usePrivy();
	const { wallets } = useSolanaWallets();
	const [isLoading, setIsLoading] = useState<string | null>(null);

	if (!isOpen) return null;

	const handleLogin = async (method: string, loginMethods: string[]) => {
		if (!ready || authenticated) return;

		setIsLoading(method);
		try {
			await login({ loginMethods });
			onClose();
		} catch (error) {
			console.error("Login failed:", error);
		} finally {
			setIsLoading(null);
		}
	};

	const authOptions = [
		{
			id: "wallet",
			label: "Solana Wallet",
			description: "Connect with Phantom, Solflare, or other Solana wallets",
			icon: Wallet,
			loginMethods: ["wallet"],
			bgColor: "bg-purple-500/10 hover:bg-purple-500/20",
			textColor: "text-purple-700",
			borderColor: "border-purple-200 hover:border-purple-300",
		},
		{
			id: "google",
			label: "Google",
			description: "Continue with your Google account",
			icon: () => (
				<svg className="h-5 w-5" viewBox="0 0 24 24">
					<path
						fill="#4285F4"
						d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
					/>
					<path
						fill="#34A853"
						d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
					/>
					<path
						fill="#FBBC05"
						d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
					/>
					<path
						fill="#EA4335"
						d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
					/>
				</svg>
			),
			loginMethods: ["google"],
			bgColor: "bg-blue-500/10 hover:bg-blue-500/20",
			textColor: "text-blue-700",
			borderColor: "border-blue-200 hover:border-blue-300",
		},
		{
			id: "twitter",
			label: "X (Twitter)",
			description: "Continue with your X account",
			icon: () => (
				<svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
					<path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
				</svg>
			),
			loginMethods: ["twitter"],
			bgColor: "bg-slate-500/10 hover:bg-slate-500/20",
			textColor: "text-slate-700",
			borderColor: "border-slate-200 hover:border-slate-300",
		},
		{
			id: "email",
			label: "Email",
			description: "Sign in with your email address",
			icon: Mail,
			loginMethods: ["email"],
			bgColor: "bg-green-500/10 hover:bg-green-500/20",
			textColor: "text-green-700",
			borderColor: "border-green-200 hover:border-green-300",
		},
	];

	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4" role="dialog" aria-modal="true">
			<div className="w-full max-w-md rounded-xl border bg-background p-6 shadow-lg">
				<div className="mb-6 flex items-center justify-between">
					<div>
						<h2 className="text-xl font-semibold">Sign In</h2>
						<p className="text-sm text-muted-foreground">Choose your preferred method</p>
					</div>
					<button
						type="button"
						onClick={onClose}
						className="rounded-lg p-2 text-muted-foreground transition hover:bg-muted"
						aria-label="Close modal"
					>
						<X className="h-4 w-4" />
					</button>
				</div>

				<div className="space-y-3">
					{authOptions.map((option) => {
						const Icon = option.icon;
						const isCurrentLoading = isLoading === option.id;

						return (
							<button
								key={option.id}
								type="button"
								onClick={() => handleLogin(option.id, option.loginMethods)}
								disabled={!ready || isCurrentLoading}
								className={`relative w-full rounded-lg border p-4 text-left transition ${option.bgColor} ${option.borderColor} disabled:cursor-not-allowed disabled:opacity-50`}
							>
								<div className="flex items-center gap-3">
									<div className={`rounded-lg bg-white p-2 shadow-sm ${option.textColor}`}>
										{isCurrentLoading ? (
											<div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
										) : (
											<Icon />
										)}
									</div>
									<div className="flex-1">
										<div className="font-medium">{option.label}</div>
										<div className="text-sm text-muted-foreground">{option.description}</div>
									</div>
									<LogIn className={`h-4 w-4 ${option.textColor}`} />
								</div>
							</button>
						);
					})}
				</div>

				<div className="mt-6 text-center">
					<p className="text-xs text-muted-foreground">
						By signing in, you agree to our Terms of Service and Privacy Policy.
						We'll create a Solana wallet for you if you don't have one.
					</p>
				</div>
			</div>
		</div>
	);
}