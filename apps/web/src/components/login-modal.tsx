"use client";

import { useState } from "react";
import { usePrivy, useCrossAppAccounts } from "@privy-io/react-auth";
import { X, Wallet, Mail, LogIn } from "lucide-react";

interface LoginModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export default function LoginModal({ isOpen, onClose }: LoginModalProps) {
	const { login, ready, authenticated } = usePrivy();
	const { loginWithCrossAppAccount } = useCrossAppAccounts();
	const [isLoading, setIsLoading] = useState<string | null>(null);

	if (!isOpen) return null;

	const handleStandardLogin = async (method: string, loginMethods: string[]) => {
		if (!ready || authenticated) return;

		setIsLoading(method);
		try {
			await login({ loginMethods });
			onClose();
		} catch (error) {
			console.error(`${method} login failed:`, error);
		} finally {
			setIsLoading(null);
		}
	};

	const handleGlobalWalletLogin = async (appId: string, providerName: string) => {
		if (!ready) return;

		setIsLoading(providerName);
		try {
			await loginWithCrossAppAccount({ appId });
			onClose();
		} catch (error) {
			console.error(`${providerName} global wallet login failed:`, error);
		} finally {
			setIsLoading(null);
		}
	};

	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
			<div className="relative w-full max-w-md rounded-2xl border bg-background p-6 shadow-xl">
				<button
					type="button"
					onClick={onClose}
					className="absolute right-4 top-4 rounded-full p-2 text-muted-foreground transition hover:bg-muted hover:text-foreground"
				>
					<X className="h-4 w-4" />
				</button>

				<div className="mb-6">
					<h2 className="text-xl font-semibold">Sign in to JAIBET</h2>
					<p className="text-sm text-muted-foreground">
						Choose your preferred sign-in method
					</p>
				</div>

				<div className="space-y-3">
					{/* Solana Wallet Login */}
					<button
						type="button"
						onClick={() => handleStandardLogin("wallet", ["wallet"])}
						disabled={!ready || isLoading !== null}
						className="flex w-full items-center justify-center gap-3 rounded-xl border border-purple-200 bg-purple-50 px-4 py-3 text-sm font-medium text-purple-700 transition hover:bg-purple-100 disabled:cursor-not-allowed disabled:opacity-50"
					>
						{isLoading === "wallet" ? (
							<div className="h-4 w-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent" />
						) : (
							<Wallet className="h-4 w-4" />
						)}
						Connect Solana Wallet
					</button>

					{/* Google Login */}
					<button
						type="button"
						onClick={() => handleStandardLogin("google", ["google"])}
						disabled={!ready || isLoading !== null}
						className="flex w-full items-center justify-center gap-3 rounded-xl border border-blue-200 bg-blue-50 px-4 py-3 text-sm font-medium text-blue-700 transition hover:bg-blue-100 disabled:cursor-not-allowed disabled:opacity-50"
					>
						{isLoading === "google" ? (
							<div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
						) : (
							<div className="h-4 w-4 rounded-full bg-blue-500" />
						)}
						Continue with Google
					</button>

					{/* X (Twitter) Login */}
					<button
						type="button"
						onClick={() => handleStandardLogin("twitter", ["twitter"])}
						disabled={!ready || isLoading !== null}
						className="flex w-full items-center justify-center gap-3 rounded-xl border border-slate-200 bg-slate-50 px-4 py-3 text-sm font-medium text-slate-700 transition hover:bg-slate-100 disabled:cursor-not-allowed disabled:opacity-50"
					>
						{isLoading === "twitter" ? (
							<div className="h-4 w-4 animate-spin rounded-full border-2 border-slate-600 border-t-transparent" />
						) : (
							<div className="h-4 w-4 rounded-full bg-slate-500" />
						)}
						Continue with X
					</button>

					{/* Email Login (Optional) */}
					<button
						type="button"
						onClick={() => handleStandardLogin("email", ["email"])}
						disabled={!ready || isLoading !== null}
						className="flex w-full items-center justify-center gap-3 rounded-xl border border-gray-200 bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 transition hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
					>
						{isLoading === "email" ? (
							<div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-600 border-t-transparent" />
						) : (
							<Mail className="h-4 w-4" />
						)}
						Continue with Email
					</button>
				</div>

				<div className="mt-6 text-center">
					<p className="text-xs text-muted-foreground">
						By signing in, you agree to our Terms of Service and Privacy Policy.
						We use Privy for secure authentication and wallet management.
					</p>
				</div>
			</div>
		</div>
	);
}
