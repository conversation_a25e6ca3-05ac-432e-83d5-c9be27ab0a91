Below is a **full, end‑to‑end Product Requirements Document (PRD)** and technical spec to take your Solana‑based prediction market from **0 → 1** using your chosen stack.

---

# 0. One‑pager (Executive summary)

**Product**: A mobile‑first, Polymarket‑style prediction market on **Solana**, with a **news feed → one‑tap For/Against** UX, **fast fiat on‑ramp**, **social/embedded wallets**, **optimistic resolution** for news (subjective) markets, **oracle‑resolved** price (objective) markets, **leaderboards**, and an **optional agent** (AISDK) with a minimal toolset (`x402` payments + betting tools).

**Why now**: Prediction markets benefit from fast, cheap settlement; Solana’s throughput/fees make it ideal. For **subjective news** markets we mirror Polymarket’s **UMA‑style optimistic resolution** (proposal → challenge → finalize) and for **objective price** markets we integrate **on‑chain price oracles** (e.g., Chainlink/Pyth/Switchboard). Polymarket publicly documents UMA‑based resolution and uses midpoint/last‑trade pricing on its CLOB; we’ll start with a CPMM and keep a path to a CLOB later. ([Polymarket][1])
Polymarket has also announced **Chainlink integration** to resolve price‑based markets quickly—our Solana design mirrors that split (objective markets via a deterministic price feed; subjective via an optimistic game). ([The Block][2])

**Stack (as requested)**

* **DB + Backend**: Convex (queries/mutations/actions, realtime) ([Convex][3])
* **Auth & Wallets**: Privy (social + embedded wallets + Solana Standard wallets incl. Phantom) ([Privy Docs][4])
* **Pkg/Runtime**: Bun (+ Next.js compatibility) ([Bun][5])
* **Frontend**: Next.js (App Router) + PWA + Tailwind
* **Monorepo**: Turborepo (Bun ok) ([Turborepo][6])
* **Agent**: Vercel **AI SDK** (tools + function calling) ([Vercel][7])
* **Models via OpenRouter**: **Grok 4 Fast** (+ search or `:online` suffix) and **GPT‑5** for hard tasks. ([OpenRouter][8])
* **Fiat → USDC (Solana)**: **Stripe Stablecoin** payments + **Transak** widget ([Stripe Docs][9])

---

# 1. Goals, Non‑Goals, Success Metrics

## 1.1 Goals

* **Frictionless onboarding**: social login → embedded wallet → fiat on‑ramp in < 2 minutes (design target).
* **Fast betting**: news feed with inline **For/Against**; confirm sheet shows cost, slippage, and payout preview.
* **Trustable settlement**:

  * **Objective (price)** markets resolve via on‑chain oracle (auto finalize). ([Chainlink Documentation][10])
  * **Subjective (news)** markets resolve via **optimistic** flow like Polymarket’s UMA pipeline. ([Polymarket][1])
* **Real‑time state**: positions, odds, PnL, and leaderboards update live (Convex streams). ([Convex][3])
* **Mobile‑first PWA** to avoid app store policy friction and to ship faster; install prompts + push notifications. ([MDN Web Docs][11])

## 1.2 Non‑Goals (v1)

* No native apps (iOS/Android).
* No complex multi‑outcome markets (binary only in v1).
* No full CLOB (v2 scope); v1 uses a CPMM.

## 1.3 Success Metrics (first 90 days post‑beta)

* **D1** funnel: 40% login → 25% wallet initialized → 12% funded → 8% trade.
* **Median time to first trade**: ≤ 4 minutes (from “Start”).
* **Resolution latency**:

  * Objective markets: auto within **≤ 5 minutes** of end time (design target; depends on oracle cadence). ([Chainlink Documentation][10])
  * Subjective markets: finalization after liveness window (configurable, default 24h).
* **DAU**: 2k+; **7‑day retention** ≥ 25%.

---

# 2. Users & Core Use Cases

* **Casuals**: want a **news feed** with simple yes/no decisions and Apple/Google Pay‑like funding via Stripe stablecoin checkout (settles to Stripe balance). ([Stripe Docs][12])
* **Power traders**: want fast fills, live odds, and transparent rules/resolution sources.
* **Curators/Resolvers** (internal allowlist at launch): create markets, publish clarifications, propose resolutions, and handle disputes.

**Primary journeys**

1. **See a headline → Bet** For/Against (embedded wallet) → Confirm → See position/odds update
2. **Fund** with card → USDC (Solana) via Stripe/Transak flow ([Stripe Docs][9])
3. **Resolution**: oracle or optimistic proposal → (optional) dispute → finalize → auto payouts

---

# 3. Product Scope & UX

## 3.1 Information architecture

* **Home**: News feed of market cards (source, time, Grok summary, odds, For/Against).
* **Market**: Rules, end time, resolution source(s), price chart, trade sheet, order history, clarifications, comments.
* **Portfolio**: Open positions (YES/NO), value, PnL, history.
* **Leaderboard**: 24h / 7d / 30d / all‑time by realized PnL.
* **Notifications**: fills, disputes, and resolution results via Web Push. ([MDN Web Docs][13])

## 3.2 UX requirements

* **Onboarding**

  * **Privy** auth: Google/X/Twitter/email + **embedded wallets** (default); **Solana Standard wallets** (Phantom) for power users. ([Privy Docs][4])
  * Note: Google OAuth often **disallows embedded webviews**; prompt users to open in system browser if detected. ([Google for Developers][14])
* **Funding**

  * **Stripe Stablecoin Payments**: accept USDC on **Solana**; funds settle fiat to Stripe balance. ([Stripe Docs][9])
  * **Transak** widget for global fiat ramps (USDC on Solana available). ([Transak Docs][15])
* **PWA**

  * Manifest + service worker to be installable and support push. ([MDN Web Docs][11])
* **Accessibility**: WCAG 2.1 AA: color contrast, focus states, keyboard nav.

---

# 4. Market Design & Resolution

## 4.1 Market types (v1)

* **Binary** (YES/NO), quoted in **USDC** (SPL), with $1 redemption for winning shares at resolution.
* **Pricing**: CPMM (constant‑product) v1; show **implied probability** from pool reserves. (Polymarket displays midpoint of CLOB bid/ask or last trade when spread > 10¢; we document for parity if/when we adopt a book.) ([Polymarket][16])

## 4.2 Resolution paths

* **Objective (price) markets**:

  * **Oracle‑resolved** (auto): integrate **Chainlink Data Feeds on Solana** or **Pyth**; finalize when `end_ts` reached and price condition met. ([Chainlink Documentation][10])
  * Rationale: Polymarket partnered with **Chainlink** to resolve price markets faster; we emulate on Solana using available Chainlink/Pyth feeds. ([The Block][2])
* **Subjective (news) markets**:

  * **Optimistic resolution**: `propose_resolution` (bonded) → challenge window → `finalize` or `escalate`. Mirrors Polymarket’s UMA‑style model (proposal, bond slashing if wrong). ([Polymarket][1])

**Rule hygiene**: Each market stores a canonical **Rules text + hash**, resolution sources (links), end time, scope exclusions, and examples. “Clarifications” allowed if essence unchanged (as Polymarket documents). ([Polymarket][17])

---

# 5. System Architecture

## 5.1 High‑level diagram (v1)

```
[Client (PWA, Next.js app)] 
   |  Privy auth + wallets         |  Convex client (realtime queries)
   |-------------------------------|----------------------------------------- 
   |                               |
   v                               v
[Next.js API routes]  ----->  [Convex Functions]
   |  sign/prepare tx                  |  queries: feed/markets/positions
   |  verify session (Privy)           |  mutations: trades, markets, clarifications
   v                                   |  actions: indexer, PnL snapshots, notifications
[Solana Anchor Program] <--------------|
   |   markets, CPMM, resolution
   v
[SPL USDC vault]   [YES/NO share mints]

   External:
   - Oracles: Chainlink/Pyth (objective markets)  ── auto-finalize
   - Agent: AISDK tools (x402.pay, bet-*), models via OpenRouter
   - On‑ramp: Stripe/Transak
```

**Key tech**:

* **Convex** provides realtime queries/mutations/actions for the app backend. ([Convex][3])
* **Anchor** for on‑chain state + instructions. ([Anchor Lang][18])
* **SPL Token / Token‑2022** for share mints and USDC vault. ([Solana][19])
* **Chainlink/Pyth/Switchboard** provide price inputs and attestations where needed. ([Chainlink Documentation][10])

---

# 6. On‑chain Program (Anchor) — spec

## 6.1 Accounts

* `Market` (PDA):

  * `authority`, `base_mint` (USDC), `yes_mint`, `no_mint`, `vault`, `fee_bps`
  * `status` (Open/Suspended/Resolving/Resolved)
  * Timing: `created_ts`, `end_ts`
  * CPMM: `reserves_yes`, `reserves_no` (USDC‑denominated)
  * Resolution: `mode` (Optimistic|Oracle), `resolution` (Unresolved|Yes|No|Invalid), `bond_vault`
* `UserPosition` (PDA (market,user)): `yes_shares`, `no_shares`, `cost_basis`, `realized_pnl`
* `ResolutionState` (PDA): `proposer`, `evidence_uri`, `proposed_outcome`, `bond_amount`, `proposed_at`, `challenged`, `finalized`

## 6.2 Instructions

* `initialize_market(params)` → creates Market, mints, vault, rules hash, end_ts
* `fund_pool(amount)` → seed initial liquidity
* `buy_yes(amount_usdc, min_shares)` / `buy_no(...)` → CPMM swap with slippage cap
* `sell_yes(shares, min_usdc)` / `sell_no(...)` → redeem via CPMM
* **Optimistic flow**: `propose_resolution(outcome, evidence_uri, bond)` → `dispute_resolution(bond)` → `finalize_resolution()` (after liveness)
  (Pattern mirrors UMA OO v3 “escalation game”). ([UMA Documentation][20])
* **Oracle flow**: `set_oracle_feed(feed_id)` (admin/allowlist) → `oracle_finalize()` validates price proof (Chainlink/Pyth/Switchboard) and sets `resolution`. ([Chainlink Documentation][10])
* `redeem_winning_shares()` → burn winning YES/NO, pay $1/share from USDC vault

## 6.3 Events & invariants

* Emit events on **trade**, **propose**, **dispute**, **finalize**, **redeem**; index off‑chain.
* Invariants:

  * CPMM k monotonic with fees to treasury
  * No redemption before `status=Resolved`
  * Bond slashing is deterministic; loser’s bond → treasury, winner’s bond + reward returned.

---

# 7. Off‑chain Data Model (Convex)

**Tables**

```
users           { _id, privy_user_id, wallet, kyc_level, country, createdAt }
markets         { _id, slug, title, category, rules_md, rules_hash, mode, endTs,
                  onchain_pda, status, resolution_sources[], createdBy, createdAt }
news            { _id, url, title, publisher, publishedAt, summary, sentiment, markets[] }
trades          { _id, userId, marketId, side, size, price, txSig, ts }
positions       { _id, userId, marketId, yesShares, noShares, costBasis, realizedPnl }
resolutions     { marketId, proposedBy, evidenceUri, proposedAt, disputed, finalized, outcome }
clarifications  { marketId, body, createdAt, authorId }
leaderboard     { userId, pnl, window, rank, snapshotTs }
```

**Functions**

* Queries: `feed()`, `market(slug)`, `leaderboard(window)`, `portfolio()`
* Mutations: `createMarket()`, `recordTrade()`, `postClarification()`
* Actions: **indexer** (consume program logs), **PnL snapshots**, **notifications**
  Convex queries are **reactive**, streaming updates to clients; actions can call 3rd‑party APIs (e.g., Stripe). ([Convex][3])

---

# 8. Agents & AI

## 8.1 Model routing

* Default summarization/sentiment: **Grok 4 Fast** via **OpenRouter** (OpenAI‑compatible API). ([OpenRouter][8])
* Hard tasks (disputes, rule QA): **GPT‑5** via OpenRouter (choose the appropriate **openai/gpt‑5** or **gpt‑5‑chat** slug). ([OpenRouter][21])
* (Optional) add **`:online`** suffix for web search when needed (OpenRouter supports suffix routing patterns). ([GitHub][22])

## 8.2 Tools (AISDK)

* `x402.pay(endpoint, amount, memo?)`: pay per HTTP‑402 invoice in USDC; ideal for premium data or APIs. x402 is an **open web protocol** (Coinbase) using the 402 status for machine‑native payments. ([docs.cdp.coinbase.com][23])
* Betting tools: `bet-list`, `bet-for`, `bet-against` → call our API routes that prepare & return unsigned tx for the user wallet to sign.

---

# 9. Funding / Payments

* **Stripe Stablecoin Payments**: accept **USDC on Solana**; users get a hosted crypto checkout; your platform settles **fiat** to Stripe balance. ([Stripe Docs][9])
* **Transak**: in‑app on‑ramp/off‑ramp widget (broad geos/payment methods). ([Transak Docs][15])
* **x402 (optional)**: agents can autonomously pay API providers over **HTTP 402** using stablecoins (chain‑agnostic protocol). ([docs.cdp.coinbase.com][23])

---

# 10. Compliance & Risk

* **Jurisdictions**: geofence restricted regions; age‑gate ≥ 18 (or local).
* **Disclosures**: “for entertainment/education”, not investment advice.
* **KYC/AML**: require KYC before first **cash‑in** (Stripe/Transak handle portions; maintain your own checks).
* **OAuth caveat**: Google OAuth **blocks embedded webviews**; provide “Open in browser” fallback. ([Google for Developers][14])
* **Market integrity**: log all rule updates; cap proposer/disputer privileges; public audit trail; slash bonds for incorrect proposals (UMA‑style). ([Polymarket][1])

---

# 11. Non‑Functional Requirements

* **Performance**: p95 page load ≤ 2.5s on 4G; tx build/submit path UI feedback < 200ms; optimistic UI for trade preview.
* **Availability**: 99.9% app uptime; graceful degradation during RPC issues.
* **Security**: program upgrade authority on multisig + timelock; audit before mainnet; Sentry + audit logs; secrets in Vercel/Convex envs.
* **Observability**: structured logs; tx success/fail metrics; oracle lag metrics.

---

# 12. Detailed Flows

## 12.1 Bet For/Against (CPMM)

1. User taps **For/Against** → opens sheet with slider (USDC).
2. Client asks API `POST /api/bet/for` with `{marketSlug, amount}` → server prepares Anchor tx, returns **base64** unsigned tx.
3. Wallet (Privy embedded or Phantom) signs → submit → show toast & update via Convex subscription.
4. Convex action (indexer) ingests `Trade` event → updates `positions`, `trades`, adjusts pool reserves.

## 12.2 Resolution

* **Objective**: at `end_ts`, server calls `oracle_finalize` with a validated **Chainlink/Pyth** price proof (or Switchboard attestation), program sets `resolution`, users call `redeem_winning_shares`. ([Chainlink Documentation][10])
* **Subjective**: allowlisted resolver calls `propose_resolution` with evidence URI (Convex keeps links). If **no dispute** during liveness → `finalize_resolution`; else escalated decision; bonds rewarded/slashed as per rule. (Pattern aligns with UMA OO v3). ([UMA Documentation][20])

---

# 13. Data & Analytics

* **Core KPIs**: funnel steps, conversion to fund/trade, ARPU, resolution latency, dispute rates.
* **Anti‑abuse**: wash‑trade detector (same wallet pairs in short windows); IP/device heuristics.
* **Leaderboards**: snapshot **realized PnL** + mark‑to‑model for open positions.

---

# 14. Delivery Plan (milestones)

**M0 – Monorepo skeleton (1 week)**

* Turborepo + Bun workspaces: `apps/web`, `packages/program`, `packages/sdk`, `packages/agent`, `packages/ui`, `convex/`.
* Next.js PWA scaffold + Tailwind.

**M1 – Auth & Funding (1–2 weeks)**

* **Privy** social + embedded wallet + **Phantom** support. ([Privy Docs][4])
* Stripe Stablecoin checkout & Transak widget. ([Stripe Docs][12])

**M2 – Anchor CPMM (2–3 weeks)**

* Instructions: `initialize_market`, `buy_*`, `sell_*`, `redeem_*`.
* Convex indexer + realtime odds.

**M3 – Resolution v1 (2 weeks)**

* **Optimistic** proposal/challenge/finalize with bonds; admin panel for “clarifications”. (Mirrors UMA flow.) ([Polymarket][1])

**M4 – Objective markets (1–2 weeks)**

* **Chainlink/Pyth** integration for price markets; auto finalize on `end_ts`. ([Chainlink Documentation][10])

**M5 – Agent & Leaderboards (1 week)**

* AISDK tools (`x402.pay`, `bet-*`), **Grok 4 Fast** for news summarization; leaderboard snapshots. ([Vercel][7])

**M6 – Beta hardening (2 weeks)**

* Security review, rate limits, load test, a11y/QA, DR drills.

---

# 15. API (server) – OpenAPI‑style sketch

```
POST /api/auth/session           → returns user + wallet pubkey (privy)
GET  /api/feed?cursor=...        → [{ marketCard }]
GET  /api/market/:slug           → { market, rules, odds, history }
POST /api/bet/for                → { txBase64 }  // client signs & sends
POST /api/bet/against            → { txBase64 }
POST /api/resolution/propose     → { ok }
POST /api/resolution/dispute     → { ok }
POST /api/x402/pay               → { receipt }
```

**Agent tools** wrap these endpoints.

---

# 16. Frontend (Next.js) – PWA and key screens

* `app/manifest.webmanifest` + service worker to meet installability criteria; `beforeinstallprompt` flow for custom “Install App”. ([MDN Web Docs][11])
* Web Push: Notifications API + Push API (server push via action). ([MDN Web Docs][13])

**UI components** (shadcn/ui or headless): MarketCard, TradeSheet, RuleCallout, ClarificationLog, PnLBadge, LeaderboardTable, InstallNudge.

---

# 17. Oracles (objective markets)

* **Primary**: **Chainlink Data Feeds on Solana** (documented; devnet/mainnet). ([Chainlink Documentation][24])
* **Alternative**: **Pyth** price feeds (Solana receiver SDK). ([Pyth Network Docs][25])
* **Custom attestation**: **Switchboard Attestation Program** for TEE‑verified compute, if we need derived conditions. ([GitHub][26])

---

# 18. Technical appendices

## 18.1 CPMM math (binary)

* Reserves: `R_yes`, `R_no` in USDC; `k = R_yes * R_no`
* Price(YES) ≈ `R_no / (R_yes + R_no)` (implied probability)
* Buying `ΔYES` with `ΔUSDC`: solve for new reserves keeping `k` (int math, 6 decimals), apply fee.

## 18.2 Program events (for indexer)

* `Trade { market, user, side, usdc_in, shares_out, price }`
* `Proposed { market, outcome, proposer, bond }`
* `Disputed { market, disputer, bond }`
* `Finalized { market, outcome }`
* `Redeemed { market, user, shares, usdc_out }`

## 18.3 Convex examples

* **Convex is realtime**; queries subscribe to table changes. Use actions for webhooks (RPC logs), Stripe calls, and push notifications. ([Convex][27])

---

# 19. Security, Ops & SRE

* **Program**: upgrade authority on multisig + timelock; publish IDL and program IDs; log all admin actions.
* **Web/Server**: CSP, session hardening, input validation (zod), rate limits per IP/wallet.
* **Secrets**: Vercel env, Convex env; never expose **OpenRouter** keys client‑side.
* **Monitoring**: Sentry + custom metrics (RPC error rate, oracle lag, queue depths).

---

# 20. Testing Strategy

* **Anchor**: unit tests (buy/sell math), property tests; localnet harness. Anchor docs cover quickstart and local development. ([Anchor Lang][28])
* **Oracles**: mock feeds, time travel for end_ts, proof verification tests.
* **Web**: Playwright flows (login, fund, trade, redeem), a11y scans.
* **Resolutions**: simulate propose/dispute/finalize, bond slashing, invalid outcome edge cases.

---

# 21. Metrics & Telemetry

* **Acquisition**: login conversion, wallet creation rate.
* **Activation**: funded %, first trade time.
* **Engagement**: trades/user/day, comments, clarifications read.
* **Trust**: disputes per market, time‑to‑finalize.
* **Revenue**: trading fees, net from bonds slashed.

---

# 22. Deployment & DevEx

* **Bun + Next.js**: Bun used for package management & scripts; compatible with mainstream frameworks, including Next.js. ([Bun][5])
* **Turborepo**: cached builds, separate pipelines per package; Bun supported. ([Turborepo][6])
* **CI/CD**: GitHub Actions → build, lint, typecheck, test, deploy.
* **Environments**: devnet (Solana), staging, mainnet.
* **Feature flags**: gradual rollout of objective markets & agent.

---

# 23. Open Questions & v2 Roadmap

* **Orderbook**: when to migrate from CPMM to **CLOB** (OpenBook or custom)? Polymarket uses a CLOB and displays midpoint/last trade; evaluate once TVL/trader depth warrants it. ([Polymarket][16])
* **Bridged arbitration (optional)**: integrate UMA’s DVM decision via a message bridge as tie‑breaker for tough disputes. ([UMA Documentation][20])
* **Reputation & social**: follow lists, trust scores for resolvers/curators.
* **Liquidity rewards**: rebates/points for LPs.

---

# 24. Acceptance Criteria (Go/No‑Go for Beta)

* Create, fund, and trade in **binary markets** on devnet and mainnet‑beta.
* **Objective** markets: auto‑finalize from a live price feed (Chainlink or Pyth) on devnet. ([Chainlink Documentation][10])
* **Subjective** markets: propose→challenge→finalize; bonds enforced; UI shows audit log (who, when, links). ([Polymarket][1])
* **Onboarding**: login with Google/X/email via Privy; embedded wallet created; Phantom connect works. ([Privy Docs][4])
* **Funding**: Stripe USDC checkout flow succeeds; Transak widget completes to USDC on Solana. ([Stripe Docs][12])
* **PWA**: installable + push notifications for fills/resolutions (Chrome, Safari). ([MDN Web Docs][11])
* **Leaderboards**: correct ranks for 24h/7d/30d/all‑time.

---

## Appendix A — Monorepo layout

```
apps/
  web/                 # Next.js (App Router, PWA, Tailwind)
packages/
  program/             # Anchor program (lib.rs, IDL)
  sdk/                 # TS client: tx builders, PDA helpers
  agent/               # AISDK tools + OpenRouter client
  ui/                  # shared UI components
  shared/              # zod types, constants, enums
convex/                # schema + functions (queries/mutations/actions)
```

## Appendix B — Example snippets

**Privy (Solana + embedded wallet + Phantom)**
Use Privy’s Solana guides for embedded wallets & Solana Standard wallets. ([Privy Docs][4])

**Stripe Stablecoin (accept USDC on Solana)**
Follow **Crypto → Stablecoin Payments** in Stripe docs (Checkout/Elements/Intents are supported; funds settle to Stripe balance). ([Stripe Docs][12])

**AI SDK agents**
Vercel **AI SDK** documents tool‑calling agents; wire `x402.pay` and betting tools as minimal toolset. ([Vercel][7])

**OpenRouter models**
Call **Grok 4 Fast** and **GPT‑5** via the OpenAI‑compatible API; choose the appropriate provider slugs (e.g., `x-ai/grok-4-fast`, `openai/gpt-5`). ([OpenRouter][8])

**Oracles**

* Chainlink Solana feeds usage guide. ([Chainlink Documentation][10])
* Pyth receiver SDK (Solana) for on‑chain reads. ([Pyth Network Docs][25])
* Switchboard attestation (if using TEE proofs). ([GitHub][26])

**PWA**
Ensure manifest + service worker satisfy **installability**; implement Web Push for resolution alerts. ([MDN Web Docs][11])

---

## Appendix C — Example market rule template (subjective)

* **Question**: *Will [X] happen by 23:59 UTC on 2025‑10‑31?*
* **Acceptable sources**: [Primary], [Secondary].
* **Inclusion/exclusion**: …
* **Resolution**: **Optimistic**; proposer must include links + archive; challenge window **24h**.
* **Edge cases**: …
  (Behavior aligns with Polymarket’s UMA‑based process.) ([Polymarket][17])

---

## Appendix D — Risk register (top 5)

1. **Oracle availability** (objective): multi‑source fallback (Pyth ↔ Chainlink); fail to optimistic if oracle data missing (flagged in UI). ([Chainlink Documentation][10])
2. **Dispute griefing** (subjective): calibrate bond size; allow admin council while governance matures. ([UMA Documentation][20])
3. **App‑store friction**: PWA path avoids store rules for wagering.
4. **OAuth friction in in‑app browsers**: enforce external browser SSO on Google. ([Google for Developers][14])
5. **Wash trading**: heuristic detection + throttles; exclude from leaderboards.

---

## Appendix E — References (most load‑bearing)

* **Polymarket resolution (UMA optimistic)** & **price display** (midpoint/last trade). ([Polymarket][1])
* **UMA OO v3 docs** (escalation game & patterns). ([UMA Documentation][20])
* **Polymarket ↔ Chainlink** (objective price resolutions). ([The Block][2])
* **Chainlink Solana feeds / usage**; **Pyth** on Solana. ([Chainlink Documentation][10])
* **Privy + Solana** (embedded + standard wallets). ([Privy Docs][4])
* **Stripe Stablecoin Payments**; **Transak** SDK. ([Stripe Docs][12])
* **Convex** (realtime queries/mutations/actions). ([Convex][3])
* **Bun ↔ Next.js** compatibility; **Turborepo** for monorepo. ([Bun][5])
* **PWA installability & Push API**. ([MDN Web Docs][11])
* **x402** protocol for HTTP‑native payments (for agents/APIs). ([docs.cdp.coinbase.com][23])
* **Google OAuth policy** on embedded webviews (fallback to system browser). ([Google for Developers][14])

---

### What you can do next

* Green‑light **Milestone M0–M2** to stand up the skeleton (auth, funding, CPMM, indexer).
* I can also generate a **starter repo structure** (files, config, and stubs) matching this PRD if you want the scaffolding ready to clone and run with Bun in one command.

[1]: https://docs.polymarket.com/developers/resolution/UMA?utm_source=chatgpt.com "Resolution - Polymarket Documentation"
[2]: https://www.theblock.co/post/370444/polymarket-turns-to-chainlink-oracles-for-resolution-of-price-focused-bets?utm_source=chatgpt.com "Polymarket turns to Chainlink oracles for resolution of price-focused ..."
[3]: https://docs.convex.dev/functions?utm_source=chatgpt.com "Functions | Convex Developer Hub"
[4]: https://docs.privy.io/recipes/solana/getting-started-with-privy-and-solana?utm_source=chatgpt.com "Getting started with Privy and Solana - Privy Docs"
[5]: https://bun.com/docs/runtime/nodejs-apis?utm_source=chatgpt.com "Node.js compatibility – Runtime | Bun Docs"
[6]: https://turborepo.com/docs/guides/frameworks/nextjs?utm_source=chatgpt.com "Next.js | Turborepo"
[7]: https://vercel.com/docs/agents?utm_source=chatgpt.com "AI Agents on Vercel"
[8]: https://openrouter.ai/x-ai/grok-4-fast%3Afree/api?utm_source=chatgpt.com "xAI: Grok 4 Fast (free) – Run with an API | OpenRouter"
[9]: https://docs.stripe.com/crypto/stablecoin-payments?utm_source=chatgpt.com "Stablecoin payments | Stripe Documentation"
[10]: https://docs.chain.link/data-feeds/solana/using-data-feeds-solana?utm_source=chatgpt.com "Using Data Feeds Onchain (Solana) - Chainlink Documentation"
[11]: https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps/Guides/Making_PWAs_installable?utm_source=chatgpt.com "Making PWAs installable - Progressive web apps | MDN - MDN Web Docs"
[12]: https://docs.stripe.com/payments/accept-stablecoin-payments?utm_source=chatgpt.com "Accept stablecoin payments | Stripe Documentation"
[13]: https://developer.mozilla.org/en-US/docs/Web/API/Push_API?utm_source=chatgpt.com "Push API - Web APIs | MDN"
[14]: https://developers.google.com/identity/protocols/oauth2/policies?utm_source=chatgpt.com "OAuth 2.0 Policies | Google for Developers"
[15]: https://docs.transak.com/docs/sdk-on-ramp-and-off-ramp?utm_source=chatgpt.com "On-Ramp and Off-Ramp - Transak"
[16]: https://docs.polymarket.com/polymarket-learn/trading/how-are-prices-calculated?utm_source=chatgpt.com "How Are Prices Calculated? - Polymarket Documentation"
[17]: https://docs.polymarket.com/polymarket-learn/markets/how-are-markets-resolved?utm_source=chatgpt.com "How Are Prediction Markets Resolved? - docs.polymarket.com"
[18]: https://www.anchor-lang.com/?utm_source=chatgpt.com "anchor-lang.com - Introduction"
[19]: https://solana.com/docs/tokens/basics?utm_source=chatgpt.com "SPL Token Basics | Solana"
[20]: https://docs.uma.xyz/developers/optimistic-oracle-v3?utm_source=chatgpt.com "Optimistic Oracle v3 | UMA Documentation"
[21]: https://openrouter.ai/openai/gpt-5?utm_source=chatgpt.com "GPT-5 - API, Providers, Stats | OpenRouter"
[22]: https://github.com/simonw/llm-openrouter/issues/20?utm_source=chatgpt.com "OpenRouter model :suffixes · Issue #20 · simonw/llm-openrouter"
[23]: https://docs.cdp.coinbase.com/x402/docs/welcome?utm_source=chatgpt.com "Welcome to x402 - Coinbase Developer Documentation"
[24]: https://docs.chain.link/data-feeds/solana?utm_source=chatgpt.com "Data Feeds on Solana - Chainlink Documentation"
[25]: https://docs.pyth.network/price-feeds/use-real-time-data/solana?utm_source=chatgpt.com "How to Use Real-Time Data in Solana Programs - docs.pyth.network"
[26]: https://github.com/switchboard-xyz/solana_switchboard_attestation_program_sdk?utm_source=chatgpt.com "solana_switchboard_attestation_program_sdk - GitHub"
[27]: https://docs.convex.dev/realtime?utm_source=chatgpt.com "Realtime | Convex Developer Hub"
[28]: https://www.anchor-lang.com/docs/quickstart/local?utm_source=chatgpt.com "Local Development - anchor-lang.com"
