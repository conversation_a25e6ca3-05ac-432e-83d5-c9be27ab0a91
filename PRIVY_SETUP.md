# Privy Setup Instructions

## 1. Create a Privy App

1. Go to [Privy Dashboard](https://dashboard.privy.io)
2. Create a new app or select an existing one
3. Copy your App ID from the dashboard

## 2. Configure Environment Variable

Add your Privy App ID to your `.env` file:

```bash
NEXT_PUBLIC_PRIVY_APP_ID=your-app-id-here
```

## 3. Configure Login Methods in Privy Dashboard

### Enable Required Login Methods:
1. **Email**: Should be enabled by default
2. **Wallet**: Enable for Solana wallet connections
3. **Google**: Enable OAuth 2.0 for Google Sign-In
4. **X (Twitter)**: Enable OAuth for Twitter/X login

### Configure Solana:
1. Go to **Wallets** tab
2. Enable **Solana** as a supported blockchain
3. Configure external wallet connectors if needed

### OAuth Configuration:
1. **Google OAuth**:
   - Add your domain to allowed origins
   - Set redirect URI: `https://your-domain.com`

2. **X (Twitter) OAuth**:
   - Configure Twitter app in Twitter Developer Portal
   - Add OAuth credentials to Privy dashboard
   - Set callback URL: `https://auth.privy.io/oauth/twitter`

## 4. Test Authentication

Once configured, you should be able to:
- ✅ Connect Solana wallets (Phantom, Solflare, etc.)
- ✅ Sign in with Google
- ✅ Sign in with X (Twitter)
- ✅ Sign in with email (OTP)

## 5. Production Setup

For production deployment:
- Update allowed origins in Privy dashboard
- Configure proper callback URLs
- Ensure environment variables are set in production

## Current Implementation Features

- **Multi-Auth Modal**: Users can choose from 4 login methods
- **Smart Wallet Creation**: Embedded wallets created automatically
- **Social Account Display**: Shows email, username, or wallet address
- **Secure Session Management**: Proper logout and session handling
- **Mobile-Responsive UI**: Works on all device sizes

## Troubleshooting

If authentication isn't working:
1. Check that `NEXT_PUBLIC_PRIVY_APP_ID` is set correctly
2. Verify login methods are enabled in Privy Dashboard
3. Check browser console for any errors
4. Ensure domains are whitelisted in Privy settings